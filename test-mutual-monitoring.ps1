# 测试相互监视功能
Write-Host "测试双重保护服务的相互监视功能..." -ForegroundColor Green

function Test-ServiceStatus {
    param($ServiceName)
    try {
        $service = Get-Service -Name $ServiceName -ErrorAction Stop
        return $service.Status
    } catch {
        return "NotFound"
    }
}

function Test-WebResponse {
    param($Port)
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$Port" -TimeoutSec 5 -ErrorAction Stop
        return $response.StatusCode -eq 200
    } catch {
        return $false
    }
}

function Show-ServiceInfo {
    Write-Host "`n=== 当前服务状态 ===" -ForegroundColor Yellow
    
    $pmsStatus = Test-ServiceStatus "ProcessMonitorService"
    $monitorStatus = Test-ServiceStatus "Monitor"
    
    Write-Host "ProcessMonitorService: " -NoNewline
    if ($pmsStatus -eq "Running") {
        Write-Host $pmsStatus -ForegroundColor Green
    } else {
        Write-Host $pmsStatus -ForegroundColor Red
    }
    
    Write-Host "Monitor: " -NoNewline
    if ($monitorStatus -eq "Running") {
        Write-Host $monitorStatus -ForegroundColor Green
    } else {
        Write-Host $monitorStatus -ForegroundColor Red
    }
    
    Write-Host "`n=== Web服务状态 ===" -ForegroundColor Yellow
    
    $web5000 = Test-WebResponse 5000
    $web5050 = Test-WebResponse 5050
    
    Write-Host "端口5000 (ProcessMonitorService): " -NoNewline
    if ($web5000) {
        Write-Host "正常" -ForegroundColor Green
    } else {
        Write-Host "无响应" -ForegroundColor Red
    }
    
    Write-Host "端口5050 (Monitor): " -NoNewline
    if ($web5050) {
        Write-Host "正常" -ForegroundColor Green
    } else {
        Write-Host "无响应" -ForegroundColor Red
    }
}

# 显示初始状态
Show-ServiceInfo

Write-Host "`n=== 开始测试相互监视功能 ===" -ForegroundColor Cyan

# 测试1：杀死ProcessMonitorService进程
Write-Host "`n测试1：模拟杀死ProcessMonitorService进程..." -ForegroundColor Yellow
try {
    $pmsProcess = Get-Process -Name "ProcessMonitorService" -ErrorAction Stop
    Write-Host "找到ProcessMonitorService进程 (PID: $($pmsProcess.Id))"
    Write-Host "正在终止进程..."
    $pmsProcess.Kill()
    Write-Host "进程已终止，等待Monitor服务重启它..." -ForegroundColor Orange
    
    # 等待重启
    Start-Sleep -Seconds 10
    
    Show-ServiceInfo
    
    $newStatus = Test-ServiceStatus "ProcessMonitorService"
    if ($newStatus -eq "Running") {
        Write-Host "✓ 测试1通过：Monitor成功重启了ProcessMonitorService" -ForegroundColor Green
    } else {
        Write-Host "✗ 测试1失败：ProcessMonitorService未被重启" -ForegroundColor Red
    }
} catch {
    Write-Host "无法找到ProcessMonitorService进程或测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 等待一段时间
Start-Sleep -Seconds 5

# 测试2：杀死Monitor进程
Write-Host "`n测试2：模拟杀死Monitor进程..." -ForegroundColor Yellow
try {
    $monitorProcess = Get-Process -Name "Monitor" -ErrorAction Stop
    Write-Host "找到Monitor进程 (PID: $($monitorProcess.Id))"
    Write-Host "正在终止进程..."
    $monitorProcess.Kill()
    Write-Host "进程已终止，等待ProcessMonitorService重启它..." -ForegroundColor Orange
    
    # 等待重启
    Start-Sleep -Seconds 10
    
    Show-ServiceInfo
    
    $newStatus = Test-ServiceStatus "Monitor"
    if ($newStatus -eq "Running") {
        Write-Host "✓ 测试2通过：ProcessMonitorService成功重启了Monitor" -ForegroundColor Green
    } else {
        Write-Host "✗ 测试2失败：Monitor未被重启" -ForegroundColor Red
    }
} catch {
    Write-Host "无法找到Monitor进程或测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green

# 显示最终状态
Show-ServiceInfo

Write-Host "`n=== 检查事件日志 ===" -ForegroundColor Yellow
Write-Host "ProcessMonitorService最近的日志:"
try {
    $pmsEvents = Get-EventLog -LogName Application -Source "ProcessMonitorService" -Newest 3 -ErrorAction Stop
    foreach ($event in $pmsEvents) {
        $timeStr = $event.TimeGenerated.ToString("yyyy-MM-dd HH:mm:ss")
        Write-Host "  [$timeStr] $($event.Message)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "  无法读取ProcessMonitorService事件日志" -ForegroundColor Red
}

Write-Host "`nMonitor最近的日志:"
try {
    $monitorEvents = Get-EventLog -LogName Application -Source "Monitor" -Newest 3 -ErrorAction Stop
    foreach ($event in $monitorEvents) {
        $timeStr = $event.TimeGenerated.ToString("yyyy-MM-dd HH:mm:ss")
        Write-Host "  [$timeStr] $($event.Message)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "  无法读取Monitor事件日志" -ForegroundColor Red
}

Write-Host "`n测试完成！如果两个测试都通过，说明相互监视功能正常工作。" -ForegroundColor Green
