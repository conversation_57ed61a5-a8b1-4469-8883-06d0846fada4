@echo off
echo 安装相互监视的双重保护服务...

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 正在以管理员身份运行...
) else (
    echo 错误：需要以管理员身份运行此脚本！
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo ========================================
echo 第一步：停止并删除现有服务
echo ========================================

REM 停止并删除ProcessMonitorService服务（如果存在）
echo 检查并停止ProcessMonitorService服务...
sc query ProcessMonitorService >nul 2>&1
if %errorLevel% == 0 (
    echo 停止ProcessMonitorService服务...
    sc stop ProcessMonitorService
    timeout /t 3 /nobreak >nul
    echo 删除ProcessMonitorService服务...
    sc delete ProcessMonitorService
)

REM 停止并删除Monitor服务（如果已存在）
echo 检查并停止Monitor服务...
sc query Monitor >nul 2>&1
if %errorLevel% == 0 (
    echo 停止Monitor服务...
    sc stop Monitor
    timeout /t 3 /nobreak >nul
    echo 删除Monitor服务...
    sc delete Monitor
)

echo.
echo ========================================
echo 第二步：安装ProcessMonitorService服务
echo ========================================

echo 安装ProcessMonitorService服务...
sc create ProcessMonitorService binPath= "%~dp0ProcessMonitorService\bin\Debug\ProcessMonitorService.exe" start= auto DisplayName= "ProcessMonitorService"

if %errorLevel% == 0 (
    echo ProcessMonitorService服务安装成功！
) else (
    echo 错误：ProcessMonitorService服务安装失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo 第三步：安装Monitor服务
echo ========================================

echo 安装Monitor服务...
sc create Monitor binPath= "%~dp0Monitor\bin\Debug\Monitor.exe" start= auto DisplayName= "Monitor"

if %errorLevel% == 0 (
    echo Monitor服务安装成功！
) else (
    echo 错误：Monitor服务安装失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo 第四步：启动服务
echo ========================================

echo 启动ProcessMonitorService服务...
sc start ProcessMonitorService
if %errorLevel% == 0 (
    echo ProcessMonitorService服务启动成功！
) else (
    echo 警告：ProcessMonitorService服务启动失败。请检查事件日志。
)

timeout /t 2 /nobreak >nul

echo 启动Monitor服务...
sc start Monitor
if %errorLevel% == 0 (
    echo Monitor服务启动成功！
) else (
    echo 警告：Monitor服务启动失败。请检查事件日志。
)

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 双重保护服务已安装并启动：
echo - ProcessMonitorService (端口5000)
echo - Monitor (端口5050)
echo.
echo 两个服务会相互监视，如果其中一个被终止，另一个会立即重启它。
echo.
echo 您可以通过以下方式管理服务：
echo - 访问 http://localhost:5000 来控制ProcessMonitorService
echo - 访问 http://localhost:5050 来控制Monitor
echo - 使用 services.msc 来查看服务状态
echo.
echo 注意：现在即使您的孩子杀死其中一个进程，另一个会立即重启它！
echo.

pause
