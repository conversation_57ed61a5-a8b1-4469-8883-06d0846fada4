# 双重保护系统 - 相互监视功能说明

## 🛡️ 系统概述

现在您的系统包含两个相互监视的服务：
- **ProcessMonitorService** (端口5000)
- **Monitor** (端口5050)

这两个服务会每5秒检查一次对方的状态，如果发现对方被终止，会立即重新启动它。

## 🔄 相互监视机制

### ProcessMonitorService 监视 Monitor
- 每5秒检查Monitor服务状态
- 如果Monitor服务停止，自动启动它
- 如果Monitor服务不存在，自动重新安装并启动

### Monitor 监视 ProcessMonitorService  
- 每5秒检查ProcessMonitorService服务状态
- 如果ProcessMonitorService服务停止，自动启动它
- 如果ProcessMonitorService服务不存在，自动重新安装并启动

## 📦 安装步骤

### 1. 自动安装（推荐）
以管理员身份运行：
```cmd
install-both-services.bat
```

### 2. 手动安装
```cmd
# 安装ProcessMonitorService
sc create ProcessMonitorService binPath= "完整路径\ProcessMonitorService\bin\Debug\ProcessMonitorService.exe" start= auto DisplayName= "ProcessMonitorService"

# 安装Monitor
sc create Monitor binPath= "完整路径\Monitor\bin\Debug\Monitor.exe" start= auto DisplayName= "Monitor"

# 启动服务
sc start ProcessMonitorService
sc start Monitor
```

## 🧪 测试相互监视功能

以管理员身份运行PowerShell测试脚本：
```powershell
.\test-mutual-monitoring.ps1
```

该脚本会：
1. 显示当前服务状态
2. 模拟杀死ProcessMonitorService进程，验证Monitor是否重启它
3. 模拟杀死Monitor进程，验证ProcessMonitorService是否重启它
4. 显示事件日志

## 🎮 对抗孩子的策略

### 孩子可能的攻击方式：
1. ❌ **杀死单个进程** - 另一个服务会立即重启它
2. ❌ **同时杀死两个进程** - 由于检查间隔是5秒，很难做到完全同步
3. ❌ **停止服务** - 另一个服务会重新启动它
4. ❌ **删除服务** - 另一个服务会重新安装并启动它

### 系统优势：
- ✅ **双重保护**：两个独立的监控程序
- ✅ **自动恢复**：被终止后5秒内自动重启
- ✅ **自动重装**：服务被删除后自动重新安装
- ✅ **隐蔽性**：孩子不知道新的进程名称
- ✅ **持久性**：即使重启电脑，服务也会自动启动

## 🌐 Web控制界面

### ProcessMonitorService
- **地址**：http://localhost:5000
- **功能**：控制游戏监控开关，查看监控进程列表

### Monitor  
- **地址**：http://localhost:5050
- **功能**：控制游戏监控开关，查看监控进程列表

## 📊 监控状态

### 检查服务状态
```cmd
sc query ProcessMonitorService
sc query Monitor
```

### 查看事件日志
1. 打开事件查看器 (`eventvwr.msc`)
2. 展开 "Windows 日志" > "应用程序"
3. 查找来源为 "ProcessMonitorService" 和 "Monitor" 的条目

### 常见日志消息
- "服务已启动" - 服务正常启动
- "Web服务器已启动，监听端口XXXX" - Web界面可用
- "检测到XXX服务未运行" - 发现对方服务停止
- "正在启动XXX服务" - 正在重启对方服务
- "XXX服务已成功启动" - 成功重启对方服务

## 🔧 管理命令

### 停止所有服务
```cmd
sc stop ProcessMonitorService
sc stop Monitor
```

### 启动所有服务
```cmd
sc start ProcessMonitorService
sc start Monitor
```

### 删除所有服务
```cmd
sc stop ProcessMonitorService
sc delete ProcessMonitorService
sc stop Monitor  
sc delete Monitor
```

## ⚠️ 注意事项

1. **管理员权限**：安装和管理服务需要管理员权限
2. **防火墙**：确保端口5000和5050未被防火墙阻止
3. **路径依赖**：两个服务需要能找到对方的可执行文件
4. **事件日志**：定期检查事件日志以确认系统正常工作

## 🎯 效果预期

安装此双重保护系统后：
- 孩子无法通过简单的"结束进程"来停止监控
- 即使孩子发现了新的进程名称，也很难同时终止两个进程
- 系统具有自我修复能力，被破坏后会自动恢复
- 您可以通过两个不同的Web界面来管理系统

这样，您的孩子就很难绕过游戏监控系统了！
