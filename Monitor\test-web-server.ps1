# 测试Monitor服务的Web服务器
Write-Host "测试Monitor服务的Web服务器..." -ForegroundColor Green

# 检查服务状态
Write-Host "`n1. 检查Monitor服务状态:" -ForegroundColor Yellow
try {
    $service = Get-Service -Name "Monitor" -ErrorAction Stop
    Write-Host "   服务状态: $($service.Status)" -ForegroundColor Cyan
    if ($service.Status -eq "Running") {
        Write-Host "   ✓ 服务正在运行" -ForegroundColor Green
    } else {
        Write-Host "   ✗ 服务未运行" -ForegroundColor Red
    }
} catch {
    Write-Host "   ✗ 未找到Monitor服务" -ForegroundColor Red
}

# 检查端口5050是否被监听
Write-Host "`n2. 检查端口5050监听状态:" -ForegroundColor Yellow
try {
    $netstat = netstat -an | Select-String ":5050"
    if ($netstat) {
        Write-Host "   ✓ 端口5050正在被监听" -ForegroundColor Green
        Write-Host "   $netstat" -ForegroundColor Cyan
    } else {
        Write-Host "   ✗ 端口5050未被监听" -ForegroundColor Red
    }
} catch {
    Write-Host "   ✗ 无法检查端口状态" -ForegroundColor Red
}

# 测试Web服务器响应
Write-Host "`n3. 测试Web服务器响应:" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5050" -TimeoutSec 10 -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Host "   ✓ Web服务器响应正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
        Write-Host "   ✓ 可以访问 http://localhost:5050" -ForegroundColor Green
    } else {
        Write-Host "   ✗ Web服务器响应异常 (状态码: $($response.StatusCode))" -ForegroundColor Red
    }
} catch {
    Write-Host "   ✗ 无法连接到Web服务器: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   请检查:" -ForegroundColor Yellow
    Write-Host "     - Monitor服务是否正在运行" -ForegroundColor White
    Write-Host "     - 防火墙是否阻止了端口5050" -ForegroundColor White
    Write-Host "     - 事件日志中是否有错误信息" -ForegroundColor White
}

# 检查事件日志
Write-Host "`n4. 检查最近的Monitor事件日志:" -ForegroundColor Yellow
try {
    $events = Get-EventLog -LogName Application -Source "Monitor" -Newest 5 -ErrorAction Stop
    if ($events) {
        Write-Host "   最近5条Monitor事件:" -ForegroundColor Cyan
        foreach ($event in $events) {
            $timeStr = $event.TimeGenerated.ToString("yyyy-MM-dd HH:mm:ss")
            $typeColor = if ($event.EntryType -eq "Error") { "Red" } elseif ($event.EntryType -eq "Warning") { "Yellow" } else { "White" }
            Write-Host "   [$timeStr] $($event.EntryType): $($event.Message)" -ForegroundColor $typeColor
        }
    } else {
        Write-Host "   ✗ 未找到Monitor事件日志" -ForegroundColor Red
    }
} catch {
    Write-Host "   ✗ 无法读取事件日志: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n测试完成!" -ForegroundColor Green
Write-Host "如果Web服务器工作正常，您可以在浏览器中访问: http://localhost:5050" -ForegroundColor Cyan
