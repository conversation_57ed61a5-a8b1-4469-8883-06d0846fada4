@echo off
echo 安装Monitor服务...

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 正在以管理员身份运行...
) else (
    echo 错误：需要以管理员身份运行此脚本！
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 停止并删除旧的ProcessMonitorService服务（如果存在）
echo 检查并停止旧的ProcessMonitorService服务...
sc query ProcessMonitorService >nul 2>&1
if %errorLevel% == 0 (
    echo 停止ProcessMonitorService服务...
    sc stop ProcessMonitorService
    timeout /t 3 /nobreak >nul
    echo 删除ProcessMonitorService服务...
    sc delete ProcessMonitorService
)

REM 停止并删除Monitor服务（如果已存在）
echo 检查并停止Monitor服务...
sc query Monitor >nul 2>&1
if %errorLevel% == 0 (
    echo 停止Monitor服务...
    sc stop Monitor
    timeout /t 3 /nobreak >nul
    echo 删除Monitor服务...
    sc delete Monitor
)

REM 安装新的Monitor服务
echo 安装Monitor服务...
sc create Monitor binPath= "%~dp0bin\Debug\Monitor.exe" start= auto DisplayName= "Monitor"

if %errorLevel% == 0 (
    echo Monitor服务安装成功！
    echo 启动Monitor服务...
    sc start Monitor
    if %errorLevel% == 0 (
        echo Monitor服务启动成功！
        echo.
        echo 服务已安装并启动。
        echo 您可以通过以下方式管理服务：
        echo - 访问 http://localhost:5000 来控制监控功能
        echo - 使用 services.msc 来管理服务
        echo.
    ) else (
        echo 警告：服务安装成功但启动失败。请检查事件日志。
    )
) else (
    echo 错误：Monitor服务安装失败！
)

pause
