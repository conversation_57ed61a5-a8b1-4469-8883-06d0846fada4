# Monitor 服务安装和使用说明

## 概述

Monitor 是一个 Windows 服务，用于监控和管理指定的进程。它是 ProcessMonitorService 的重命名版本，具有相同的功能但使用了不同的进程名称。

## 安装方法

### 方法 1：使用自动安装脚本（推荐）

1. **以管理员身份运行** `install-monitor-service.bat`
2. 脚本会自动完成所有安装步骤

### 方法 2：手动使用 SC 命令

1. 打开**管理员命令提示符**
2. 执行以下命令：

```cmd
# 停止并删除旧服务（如果存在）
sc stop ProcessMonitorService
sc delete ProcessMonitorService

# 安装新的Monitor服务
sc create Monitor binPath= "完整路径\Monitor\bin\Debug\Monitor.exe" start= auto DisplayName= "Monitor"

# 启动服务
sc start Monitor
```

**注意：** 请将"完整路径"替换为实际的项目路径，例如：

```cmd
sc create Monitor binPath= "E:\code\repos\ProcessMonitor\Monitor\bin\Debug\Monitor.exe" start= auto DisplayName= "Monitor"
```

## 验证安装是否成功

### 1. 检查服务状态

```cmd
sc query Monitor
```

如果安装成功，应该显示服务状态为 "RUNNING"。

### 2. 检查服务管理器

1. 按 `Win + R`，输入 `services.msc`
2. 在服务列表中查找 "Monitor" 服务
3. 确认状态为 "正在运行"

### 3. 访问 Web 控制界面

打开浏览器访问：http://localhost:5050

- 如果页面正常显示，说明服务运行正常
- 可以在此页面控制监控功能的开启/关闭

### 4. 检查事件日志

1. 按 `Win + R`，输入 `eventvwr.msc`
2. 展开 "Windows 日志" > "应用程序"
3. 查找来源为 "Monitor" 的日志条目

## 功能说明

### Web 控制界面

- **地址：** http://localhost:5050
- **功能：**
  - 查看当前监控状态
  - 开启/关闭进程监控
  - 查看监控的进程列表

### 配置文件

- **位置：** Monitor.exe 同目录下的 `processconfig.json`
- **功能：** 配置要监控的进程名称列表

### 默认监控的进程

- redalert
- Dune2000
- TiberianDawn

## 管理服务

### 启动服务

```cmd
sc start Monitor
```

### 停止服务

```cmd
sc stop Monitor
```

### 删除服务

```cmd
sc stop Monitor
sc delete Monitor
```

## 故障排除

### 如果服务无法启动

1. 检查事件日志中的错误信息
2. 确认 Monitor.exe 文件存在且可执行
3. 确认所有依赖的 DLL 文件都在同一目录下

### 如果无法访问 Web 界面

1. 检查防火墙设置，确保端口 5050 未被阻止
2. 确认服务正在运行
3. 尝试使用 http://127.0.0.1:5050

### 如果进程监控不工作

1. 通过 Web 界面确认监控功能已启用
2. 检查 processconfig.json 文件中的进程名称是否正确
3. 查看事件日志中的相关信息

## 与原 ProcessMonitorService 的区别

- **进程名称：** Monitor.exe（而不是 ProcessMonitorService.exe）
- **服务名称：** Monitor（而不是 ProcessMonitorService）
- **事件日志来源：** Monitor（而不是 ProcessMonitorService）
- **功能完全相同**
