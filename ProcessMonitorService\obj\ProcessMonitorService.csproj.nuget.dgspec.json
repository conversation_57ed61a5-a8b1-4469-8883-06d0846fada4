{"format": 1, "restore": {"E:\\code\\repos\\ProcessMonitor\\ProcessMonitorService\\ProcessMonitorService.csproj": {}}, "projects": {"E:\\code\\repos\\ProcessMonitor\\ProcessMonitorService\\ProcessMonitorService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\code\\repos\\ProcessMonitor\\ProcessMonitorService\\ProcessMonitorService.csproj", "projectName": "ProcessMonitorService", "projectPath": "E:\\code\\repos\\ProcessMonitor\\ProcessMonitorService\\ProcessMonitorService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\code\\repos\\ProcessMonitor\\ProcessMonitorService\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"Microsoft.AspNetCore.Hosting": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Server.Kestrel": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.1, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.1, )"}}}}}}}