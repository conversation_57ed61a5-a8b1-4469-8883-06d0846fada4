using System;
using System.ServiceProcess;
using System.Diagnostics;
using System.Timers;
using System.Configuration;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http;
using System.Text;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Linq;
using System.Threading;

namespace GameWatcher2
{
    public class ProcessConfig
    {
        public List<string> TargetProcesses { get; set; } = new List<string>();
        public bool IsKillEnabled { get; set; } = false;
    }

    public partial class GameWatcher2Service : ServiceBase
    {
        private System.Timers.Timer timer;
        private bool isKillEnabled = false;
        private IWebHost webHost;
        private string configPath;
        private FileSystemWatcher configWatcher;
        private ProcessConfig currentConfig;

        public GameWatcher2Service()
        {
            ServiceName = "GameWatcher2";
            // 获取程序所在目录
            string exePath = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
            configPath = Path.Combine(exePath, "processconfig.json");

            // 如果配置文件不存在，创建默认配置
            if (!File.Exists(configPath))
            {
                var defaultConfig = new ProcessConfig
                {
                    TargetProcesses = new List<string>
                    {
                        "redalert",
                        "Dune2000",
                        "TiberianDawn"
                    }
                };
                File.WriteAllText(configPath, JsonSerializer.Serialize(defaultConfig, new JsonSerializerOptions
                {
                    WriteIndented = true
                }));
            }

            LoadConfig();
            SetupConfigWatcher();
        }

        private void LoadConfig()
        {
            try
            {
                string jsonContent = File.ReadAllText(configPath);
                currentConfig = JsonSerializer.Deserialize<ProcessConfig>(jsonContent);
                isKillEnabled = currentConfig.IsKillEnabled;  // 从配置加载状态
                WriteToEventLog($"配置已加载，监控进程: {string.Join(", ", currentConfig.TargetProcesses)}, 监控状态: {(isKillEnabled ? "启用" : "禁用")}");
            }
            catch (Exception ex)
            {
                WriteToEventLog($"加载配置文件失败: {ex.Message}");
                // 如果加载失败，使用空列表
                currentConfig = new ProcessConfig();
                isKillEnabled = false;
            }
        }

        private void SetupConfigWatcher()
        {
            string directory = Path.GetDirectoryName(configPath);
            configWatcher = new FileSystemWatcher(directory, "processconfig.json");

            configWatcher.NotifyFilter = NotifyFilters.LastWrite;
            configWatcher.Changed += OnConfigChanged;
            configWatcher.EnableRaisingEvents = true;
        }

        private void OnConfigChanged(object sender, FileSystemEventArgs e)
        {
            // 添加短暂延迟确保文件写入完成
            Task.Delay(500).ContinueWith(_ =>
            {
                LoadConfig();
            });
        }

        protected override void OnStart(string[] args)
        {
            timer = new System.Timers.Timer();
            timer.Interval = 5000; // 5秒检查一次
            timer.Elapsed += new ElapsedEventHandler(OnTimer);
            timer.Start();

            StartWebServer();

            WriteToEventLog("服务已启动");
        }

        protected override void OnStop()
        {
            timer.Stop();
            configWatcher.EnableRaisingEvents = false;
            configWatcher.Dispose();
            webHost?.StopAsync().Wait();
            WriteToEventLog("服务已停止");
        }

        private void StartWebServer()
        {
            try
            {
                webHost = new WebHostBuilder()
                    .UseKestrel()
                    .UseUrls("http://*:6002")
                    .Configure(app =>
                    {
                        app.Run(async context =>
                        {
                            if (context.Request.Path == "/toggle" && context.Request.Method == "POST")
                            {
                                isKillEnabled = !isKillEnabled;
                                await context.Response.WriteAsync($"{{\"status\": \"success\", \"isKillEnabled\": {isKillEnabled.ToString().ToLower()}}}");
                                WriteToEventLog($"进程终止功能已{(isKillEnabled ? "启用" : "禁用")}");
                                return;
                            }

                            if (context.Request.Path == "/config" && context.Request.Method == "GET")
                            {
                                string html = $@"
                                <!DOCTYPE html>
                                <html>
                                <head>
                                    <title>进程配置</title>
                                    <meta charset='utf-8'>
                                    <meta name='viewport' content='width=device-width, initial-scale=1'>
                                    <style>
                                        body {{ font-family: Arial; margin: 20px; }}
                                        .process-list {{ margin: 20px 0; }}
                                    </style>
                                </head>
                                <body>
                                    <h2>当前监控的进程列表</h2>
                                    <div class='process-list'>
                                        <ul>
                                            {string.Join("", currentConfig.TargetProcesses.Select(p => $"<li>{p}</li>"))}
                                        </ul>
                                    </div>
                                    <a href='/'>&lt; 返回控制页面</a>
                                </body>
                                </html>";

                                context.Response.ContentType = "text/html; charset=utf-8";
                                await context.Response.WriteAsync(html);
                                return;
                            }

                            string mainHtml = $@"
                            <!DOCTYPE html>
                            <html>
                            <head>
                                <title>游戏进程控制 - GameWatcher2</title>
                                <meta charset='utf-8'>
                                <meta name='viewport' content='width=device-width, initial-scale=1'>
                                <style>
                                    body {{ font-family: Arial; margin: 20px; }}
                                    .status {{ margin: 20px 0; }}
                                    .button {{
                                        background-color: {(isKillEnabled ? "#ff4444" : "#44aa44")};
                                        color: white;
                                        padding: 15px 30px;
                                        border: none;
                                        border-radius: 5px;
                                        font-size: 18px;
                                        cursor: pointer;
                                        display: block;
                                        margin-bottom: 20px;
                                    }}
                                    .config-link {{
                                        color: #0066cc;
                                        text-decoration: none;
                                    }}
                                </style>
                            </head>
                            <body>
                                <h2>游戏进程控制 - GameWatcher2</h2>
                                <div class='status'>
                                    当前状态: {(isKillEnabled ? "游戏禁止运行" : "游戏允许运行")}
                                </div>
                                <button class='button' onclick='toggleStatus()'>
                                    {(isKillEnabled ? "允许游戏运行" : "禁止游戏运行")}
                                </button>
                                <a href='/config' class='config-link'>查看监控进程列表 &gt;</a>
                                <script>
                                    function toggleStatus() {{
                                        fetch('/toggle', {{ method: 'POST' }})
                                            .then(response => response.json())
                                            .then(data => {{
                                                if(data.status === 'success') {{
                                                    location.reload();
                                                }}
                                            }});
                                    }}
                                </script>
                            </body>
                            </html>";

                            context.Response.ContentType = "text/html; charset=utf-8";
                            await context.Response.WriteAsync(mainHtml);
                        });
                    })
                    .Build();

                webHost.StartAsync();
                WriteToEventLog("Web服务器已启动，监听端口6002");
            }
            catch (Exception ex)
            {
                WriteToEventLog($"Web服务器启动失败: {ex.Message}");
            }
        }

        private void OnTimer(object sender, ElapsedEventArgs e)
        {
            try
            {
                // 检查并重启GameWatcher1服务
                CheckAndRestartPartnerService();
                
                if (isKillEnabled)
                {
                    CheckAndKillProcesses();
                }
            }
            catch (Exception ex)
            {
                WriteToEventLog($"发生错误: {ex.Message}");
            }
        }

        private void CheckAndKillProcesses()
        {
            foreach (string processName in currentConfig.TargetProcesses)
            {
                Process[] processes = Process.GetProcessesByName(processName);

                if (processes.Length > 0)
                {
                    foreach (Process process in processes)
                    {
                        try
                        {
                            process.Kill();
                            WriteToEventLog($"已终止进程 {process.ProcessName} (PID: {process.Id})");
                        }
                        catch (Exception ex)
                        {
                            WriteToEventLog($"终止进程失败 {process.ProcessName} (PID: {process.Id}): {ex.Message}");
                        }
                    }
                }
            }
        }

        private void CheckAndRestartPartnerService()
        {
            try
            {
                string partnerServiceName = "GameWatcher1";
                string partnerExePath = GetPartnerServicePath();

                // 检查GameWatcher1服务状态
                ServiceController partnerService = null;
                try
                {
                    partnerService = new ServiceController(partnerServiceName);
                    if (partnerService.Status != ServiceControllerStatus.Running)
                    {
                        WriteToEventLog($"检测到{partnerServiceName}服务未运行，状态: {partnerService.Status}");

                        // 尝试启动服务
                        if (partnerService.Status == ServiceControllerStatus.Stopped)
                        {
                            WriteToEventLog($"正在启动{partnerServiceName}服务...");
                            partnerService.Start();
                            partnerService.WaitForStatus(ServiceControllerStatus.Running, TimeSpan.FromSeconds(30));
                            WriteToEventLog($"{partnerServiceName}服务已成功启动");
                        }
                    }
                }
                catch (InvalidOperationException)
                {
                    // 服务不存在，尝试重新安装并启动
                    WriteToEventLog($"{partnerServiceName}服务不存在，正在尝试重新安装...");
                    if (!string.IsNullOrEmpty(partnerExePath) && File.Exists(partnerExePath))
                    {
                        InstallAndStartPartnerService(partnerServiceName, partnerExePath);
                    }
                    else
                    {
                        WriteToEventLog($"无法找到{partnerServiceName}服务的可执行文件: {partnerExePath}");
                    }
                }
                finally
                {
                    partnerService?.Dispose();
                }
            }
            catch (Exception ex)
            {
                WriteToEventLog($"检查伙伴服务时发生错误: {ex.Message}");
            }
        }

        private string GetPartnerServicePath()
        {
            try
            {
                // 获取当前程序所在目录的父目录，然后查找GameWatcher1项目
                string currentDir = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
                string parentDir = Directory.GetParent(currentDir)?.Parent?.FullName;

                if (!string.IsNullOrEmpty(parentDir))
                {
                    string gameWatcher1Path = Path.Combine(parentDir, "GameWatcher1", "bin", "Debug", "GameWatcher1.exe");
                    if (File.Exists(gameWatcher1Path))
                    {
                        return gameWatcher1Path;
                    }
                }

                // 如果找不到，尝试相对路径
                string relativePath = Path.Combine(currentDir, "..", "..", "GameWatcher1", "bin", "Debug", "GameWatcher1.exe");
                if (File.Exists(relativePath))
                {
                    return Path.GetFullPath(relativePath);
                }

                return null;
            }
            catch (Exception ex)
            {
                WriteToEventLog($"获取伙伴服务路径时发生错误: {ex.Message}");
                return null;
            }
        }

        private void InstallAndStartPartnerService(string serviceName, string exePath)
        {
            try
            {
                // 使用SC命令安装服务
                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = $"create {serviceName} binPath= \"{exePath}\" start= auto DisplayName= \"{serviceName}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using (Process process = Process.Start(psi))
                {
                    process.WaitForExit();
                    if (process.ExitCode == 0)
                    {
                        WriteToEventLog($"{serviceName}服务安装成功");

                        // 启动服务
                        Thread.Sleep(1000); // 等待一秒确保服务注册完成
                        ServiceController newService = new ServiceController(serviceName);
                        newService.Start();
                        newService.WaitForStatus(ServiceControllerStatus.Running, TimeSpan.FromSeconds(30));
                        WriteToEventLog($"{serviceName}服务已启动");
                        newService.Dispose();
                    }
                    else
                    {
                        string error = process.StandardError.ReadToEnd();
                        WriteToEventLog($"安装{serviceName}服务失败: {error}");
                    }
                }
            }
            catch (Exception ex)
            {
                WriteToEventLog($"安装并启动{serviceName}服务时发生错误: {ex.Message}");
            }
        }

        private void WriteToEventLog(string message)
        {
            string source = "GameWatcher2";
            string log = "Application";

            if (!EventLog.SourceExists(source))
            {
                EventLog.CreateEventSource(source, log);
            }

            EventLog.WriteEntry(source, message);
        }
    }
}
